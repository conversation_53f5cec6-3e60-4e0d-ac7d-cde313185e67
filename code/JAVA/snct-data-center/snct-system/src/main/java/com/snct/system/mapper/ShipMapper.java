package com.snct.system.mapper;

import com.snct.system.domain.Ship;
import com.snct.system.domain.dto.ShipSimpleDto;

import java.util.List;

/**
 * 船只Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-04-08
 */
public interface ShipMapper 
{
    /**
     * 查询船只
     * 
     * @param shipId 船只主键
     * @return 船只
     */
    public Ship selectShipByShipId(Long shipId);
    public Ship selectShipByShipSn(String sn);

    /**
     * 查询船只列表
     * 
     * @param ship 船只
     * @return 船只集合
     */
    public List<Ship> selectShipList(Ship ship);
    public int selectShipListCount(Ship ship);

    /**
     * 根据部门ID查询船只列表
     *
     * @param deptId 部门ID
     * @return 船只集合
     */
    public List<ShipSimpleDto> selectSimpleShipListByDeptId(Long deptId);

    /**
     * 新增船只
     * 
     * @param ship 船只
     * @return 结果
     */
    public int insertShip(Ship ship);

    /**
     * 修改船只
     * 
     * @param ship 船只
     * @return 结果
     */
    public int updateShip(Ship ship);

    /**
     * 删除船只
     * 
     * @param shipId 船只主键
     * @return 结果
     */
    public int deleteShipByShipId(Long shipId);

    /**
     * 批量删除船只
     * 
     * @param shipIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteShipByShipIds(Long[] shipIds);
}
