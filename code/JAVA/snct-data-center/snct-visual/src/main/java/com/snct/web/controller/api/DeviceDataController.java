//package com.snct.web.controller.api;
//
//import com.snct.common.core.controller.BaseController;
//import com.snct.common.core.domain.AjaxResult;
//import com.snct.common.utils.StringUtils;
//import com.snct.hbase.enums.DeviceTypeEnum;
//import com.snct.service.device.AwsService;
//import com.snct.service.device.DeviceService;
//import com.snct.service.RealtimeService;
//import com.snct.service.device.GpsService;
//import com.snct.system.domain.Device;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RestController;
//
///**
// * 设备数据
// * <AUTHOR>
// * @date 2025-06-09
// */
////@CrossOrigin
//@RestController
//@RequestMapping("/api/device/data")
//public class DeviceDataController extends BaseController {
//
//    @Autowired
//    private DeviceService deviceService;
//
//    @Autowired
//    private AwsService awsService;
//
//    @Autowired
//    private RealtimeService realtimeService;
//
//    @Autowired
//    private GpsService gpsService;
//
//
//    /**
//     * 获取数据
//     */
//    @GetMapping("/list")
//    public AjaxResult list(String sn, String deviceCode, Integer interval, Long startTime, Long endTime) {
//        if (StringUtils.isBlank(sn) || StringUtils.isBlank(deviceCode)) {
//            return AjaxResult.error("获取失败，缺少参数");
//        }
//
//        Device device = deviceService.getDeviceBySnAndCode(sn, deviceCode);
//        if (device == null) {
//            return AjaxResult.error("获取失败，没有该设备");
//        }
//
//        if (DeviceTypeEnum.AWS.getValue().equals(device.getType())) {
//            return AjaxResult.success(awsService.queryByTime(sn, deviceCode, interval, startTime, endTime));
//        }
//        return AjaxResult.error("获取失败，没有该类型设备");
//    }
//
//
//    @GetMapping("/test/getLatestData")
//    public AjaxResult getLatestData(String sn, String deviceCode) {
//        return AjaxResult.success(realtimeService.getLatestData(sn, deviceCode,null));
//    }
//
//    @GetMapping("/test/getDataList")
//    public AjaxResult getDataList(String sn, String deviceCode) {
//        return AjaxResult.success(gpsService.getGpsData("100", sn,deviceCode, 7, 1751610903000L, 1754289303000L));
//    }
//}
