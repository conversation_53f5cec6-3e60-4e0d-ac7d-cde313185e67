package com.snct.web.controller.system;

import com.anji.captcha.service.CaptchaService;
import com.snct.common.constant.Constants;
import com.snct.common.core.domain.AjaxResult;
import com.snct.common.core.domain.entity.SysMenu;
import com.snct.common.core.domain.entity.SysRole;
import com.snct.common.core.domain.entity.SysUser;
import com.snct.common.core.domain.model.LoginBody;
import com.snct.common.core.domain.model.LoginUser;
import com.snct.common.exception.ServiceException;
import com.snct.common.exception.user.CaptchaException;
import com.snct.common.exception.user.NoPermissionException;
import com.snct.common.exception.user.UserPasswordNotMatchException;
import com.snct.common.utils.MessageUtils;
import com.snct.common.utils.SecurityUtils;
import com.snct.framework.manager.AsyncManager;
import com.snct.framework.manager.factory.AsyncFactory;
import com.snct.framework.security.context.AuthenticationContextHolder;
import com.snct.framework.web.service.SysLoginService;
import com.snct.framework.web.service.SysPermissionService;
import com.snct.framework.web.service.TokenService;
import com.snct.system.service.ISysMenuService;
import com.snct.system.service.ISysRoleService;
import com.snct.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;
import com.anji.captcha.model.common.ResponseModel;
import com.anji.captcha.model.vo.CaptchaVO;
import com.anji.captcha.service.CaptchaService;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;

/**
 * 登录验证
 * 
 * <AUTHOR>
 */
@RestController
public class SysLoginController
{
    @Autowired
    private SysLoginService loginService;

    @Autowired
    private ISysMenuService menuService;

    @Autowired
    private ISysUserService userService;

    @Autowired
    private ISysRoleService roleService;

    @Autowired
    private SysPermissionService permissionService;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private CaptchaService captchaService;
    @Resource
    private AuthenticationManager authenticationManager;

    /**
     * 登录方法
     * 
     * @param username 登录信息
     * @return 结果
     */
    @PostMapping("/login")
    public AjaxResult login(String username, String password, String code)
    {
        AjaxResult ajax = AjaxResult.success();
        // 生成令牌
        Integer type = 1;  //验证验证码   其他不验证验证码
        String token = loginV(username, password,type, code);
        ajax.put(Constants.TOKEN, token);
        return ajax;
    }


    /**
     * 登录验证
     *
     * @param username 用户名
     * @param password 密码
     * @param code 验证码
     * @return 结果
     */
    public String loginV(String username, String password, Integer type ,String code)
    {
        // 验证码校验
        if (type == 1) {
            CaptchaVO captchaVO = new CaptchaVO();
            captchaVO.setCaptchaVerification(code);
            ResponseModel response = captchaService.verification(captchaVO);
            if (!response.isSuccess()) {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.error")));
                throw new CaptchaException();
            }
        }
        // 登录前置校验
        loginService.loginPreCheck(username, password);
        //登录验证用户是否拥有系统登录权限
        loginManageSysPermission(username);
        // 用户验证
        Authentication authentication = null;
        try
        {
            UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(username, password);
            AuthenticationContextHolder.setContext(authenticationToken);
            // 该方法会去调用UserDetailsServiceImpl.loadUserByUsername
            authentication = authenticationManager.authenticate(authenticationToken);
        }
        catch (Exception e)
        {
            if (e instanceof BadCredentialsException)
            {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.password.not.match")));
                throw new UserPasswordNotMatchException();
            }
            else
            {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, e.getMessage()));
                throw new ServiceException(e.getMessage());
            }
        }
        finally
        {
            AuthenticationContextHolder.clearContext();
        }
        AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.success")));
        LoginUser loginUser = (LoginUser) authentication.getPrincipal();
        loginService.recordLoginInfo(loginUser.getUserId());
        // 生成token
        return tokenService.createToken(loginUser);
    }

    /**
     * 登录验证用户是否拥有系统登录权限
     * @param username 用户名
     */
    public void loginManageSysPermission(String username)
    {
        // 登录验证用户是否拥有系统登录权限
        SysUser user = userService.selectUserByUserName(username);
        String key = "";
        if(user==null){
            key = "no";
        }else{
            List<SysRole> roleList = roleService.selectUserRolesByUserId(user.getUserId());
            String authSys = "";
            for(SysRole role : roleList){
                authSys = authSys + role.getAuthSys()+",";
            }
            //可视化系统
            if(authSys.indexOf("dataDisplaySys")==-1){
                key = "no";
            }
        }
        if (!"".equals(key))
        {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.no.permission")));
            throw new NoPermissionException();
        }
    }


    /**
     * 获取用户信息
     * 
     * @return 用户信息
     */
    @GetMapping("getInfo")
    public AjaxResult getInfo()
    {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUser user = loginUser.getUser();
        // 角色集合
        Set<String> roles = permissionService.getRolePermission(user);
        // 权限集合
        Set<String> permissions = permissionService.getMenuPermission(user);
        if (!loginUser.getPermissions().equals(permissions))
        {
            loginUser.setPermissions(permissions);
            tokenService.refreshToken(loginUser);
        }
        AjaxResult ajax = AjaxResult.success();
        ajax.put("user", user);
        ajax.put("roles", roles);
        ajax.put("permissions", permissions);
        return ajax;
    }

    /**
     * 获取路由信息
     * 
     * @return 路由信息
     */
    @GetMapping("getRouters")
    public AjaxResult getRouters()
    {
        Long userId = SecurityUtils.getUserId();
        List<SysMenu> menus = menuService.selectMenuTreeByUserId(userId);
        return AjaxResult.success(menuService.buildMenus(menus));
    }


    /**
     * 个人信息
     */
    @GetMapping("/profile")
    public AjaxResult profile()
    {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUser user = null;
        if(loginUser!=null){
            user = loginUser.getUser();
        }else{
            user = new SysUser();
            user.setUserName("");
        }
        AjaxResult ajax = AjaxResult.success(user);
        return ajax;
    }

}
