package com.snct.kafka.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.snct.common.core.redis.RedisCache;
import com.snct.hbase.service.StoreService;
import com.snct.kafka.KafkaMessage;
import com.snct.system.domain.Device;
import com.snct.system.domain.Ship;
import com.snct.system.service.IDeviceService;
import com.snct.system.service.IShipService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 设备消息处理器抽象基类
 * 使用模板方法模式定义设备数据处理流程
 *
 * <AUTHOR>
 */
public abstract class AbstractDeviceHandler {

    protected final Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    protected RedisCache redisCache;

    @Autowired
    protected StoreService storeService;

    @Autowired
    protected IDeviceService deviceService;

    @Autowired
    protected IShipService shipService;

    /**
     * 处理设备数据
     *
     * @param kafkaMessage Kafka消息
     */
    public final void processDeviceData(KafkaMessage kafkaMessage) {
        try {
            // 1. 解析消息
            String msgContent = kafkaMessage.getMsg();
            JSONObject jsonObject = JSON.parseObject(msgContent);
            
            // 2. 获取设备ID和SN
            String sn = kafkaMessage.getSn();
            Long deviceId = jsonObject.getLong("deviceId");
            
            // 3. 获取设备信息
            Map<String, Long> deviceInfo = getDeviceInfo(sn, deviceId);
            
            // 4. 填充设备信息到消息
            jsonObject.put("deptId", deviceInfo.get("deptId"));
            jsonObject.put("shipId", deviceInfo.get("shipId"));
            kafkaMessage.setMsg(jsonObject.toJSONString());
            
            // 5. 解析数据并保存到MySQL
            boolean mysqlResult = saveToMysql(jsonObject, sn, deviceInfo);
            
            // 6. 保存到HBase
            boolean hbaseResult = saveToHbase(kafkaMessage);
            
            // 7. 记录结果
            logResult(mysqlResult, hbaseResult, deviceId, deviceInfo);
            
        } catch (Exception e) {
            handleException(e, kafkaMessage);
        }
    }
    
    /**
     * 获取设备信息(dept_id和ship_id)
     *
     * @param sn       船舶SN
     * @param deviceId 设备ID
     * @return 包含deptId和shipId的Map
     */
    protected Map<String, Long> getDeviceInfo(String sn, Long deviceId) {
        Map<String, Long> result = new HashMap<>();
        // 默认值设置为0而非null，避免后续处理出现问题
        result.put("deptId", 0L);
        result.put("shipId", 0L);

        try {
            // 参数检查
            if (deviceId == null) {
                logger.warn("获取设备信息时，设备ID为null");
                return result;
            }

            if (StringUtils.isEmpty(sn)) {
                logger.warn("获取设备信息时，船舶SN为空");
            }

            // 查询设备信息
            Device queryDevice = new Device();
            queryDevice.setDaDeviceId(deviceId);
            List<Device> devices = deviceService.selectDeviceList(queryDevice);

            if (devices != null && !devices.isEmpty()) {
                Device device = devices.get(0);
                if (device.getDeptId() != null) {
                    result.put("deptId", device.getDeptId());
                } else {
                    logger.warn("设备(ID:{})的部门ID为null", deviceId);
                }

                // 查询船舶信息
                if (!StringUtils.isEmpty(sn)) {
                    Ship queryShip = new Ship();
                    queryShip.setSn(sn);
                    List<Ship> ships = shipService.selectShipList(queryShip);
                    if (ships != null && !ships.isEmpty()) {
                        Ship ship = ships.get(0);
                        if (ship.getShipId() != null) {
                            result.put("shipId", ship.getShipId());
                        } else {
                            logger.warn("船舶(SN:{})的ID为null", sn);
                        }
                    } else {
                        logger.warn("未找到SN为{}的船舶信息", sn);
                    }
                }
            } else {
                logger.warn("未找到设备信息，DaDeviceId: {}", deviceId);
            }
        } catch (Exception e) {
            logger.error("获取设备信息异常: {}", e.getMessage(), e);
        }

        return result;
    }

    /**
     * 记录处理结果
     *
     * @param mysqlResult MySQL保存结果
     * @param hbaseResult HBase保存结果
     * @param deviceId 设备ID
     * @param deviceInfo 设备信息
     */
    protected void logResult(boolean mysqlResult, boolean hbaseResult, Long deviceId, Map<String, Long> deviceInfo) {
        if (mysqlResult && hbaseResult) {
            logger.info("设备数据保存到Mysql和Hbase成功，DeviceId: {}, DeptId: {}, ShipId: {}",
                    deviceId, deviceInfo.get("deptId"), deviceInfo.get("shipId"));
        } else if (!mysqlResult) {
            logger.error("保存到MySQL失败，DeviceId: {}", deviceId);
        } else {
            logger.error("保存到HBase失败，DeviceId: {}", deviceId);
        }
    }

    /**
     * 处理异常
     *
     * @param e 异常
     * @param kafkaMessage Kafka消息
     */
    protected void handleException(Exception e, KafkaMessage kafkaMessage) {
        logger.error("处理设备数据异常: {}, 消息内容: {}", e.getMessage(), kafkaMessage.getMsg(), e);
    }

    /**
     * 保存数据到MySQL
     * 子类必须实现此方法，处理特定设备的MySQL存储逻辑
     *
     * @param jsonObject JSON对象
     * @param sn 船舶SN
     * @param deviceInfo 设备信息
     * @return 是否保存成功
     */
    protected abstract boolean saveToMysql(JSONObject jsonObject, String sn, Map<String, Long> deviceInfo);

    /**
     * 保存数据到HBase
     * 子类必须实现此方法，处理特定设备的HBase存储逻辑
     *
     * @param kafkaMessage Kafka消息
     * @return 是否保存成功
     */
    protected abstract boolean saveToHbase(KafkaMessage kafkaMessage);
} 