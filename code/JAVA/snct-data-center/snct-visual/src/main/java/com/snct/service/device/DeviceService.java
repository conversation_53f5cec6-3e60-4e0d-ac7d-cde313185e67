//package com.snct.service.device;
//
//import com.snct.common.annotation.DataScope;
//import com.snct.system.domain.Device;
//import com.snct.system.mapper.DeviceMapper;
//import com.snct.system.service.IShipService;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.data.redis.core.RedisTemplate;
//import org.springframework.data.redis.core.ValueOperations;
//import org.springframework.stereotype.Service;
//
//import java.util.List;
//
//@Service
//public class DeviceService {
//    @Autowired
//    private DeviceMapper deviceMapper;
//    @Autowired
//    private IShipService shipService;
//    @Autowired
//    private RedisTemplate redisTemplate;
//
//    /**
//     * 查询设备信息
//     *
//     * @param deviceId 设备ID
//     * @return 设备信息
//     */
//    public Device selectDeviceById(Long deviceId) {
//
//        return deviceMapper.selectDeviceById(deviceId);
//    }
//
//    /**
//     * 查询设备列表
//     *
//     * @param device 设备信息
//     * @return 设备集合
//     */
//    @DataScope(deptAlias = "d")
//    public List<Device> selectDeviceList(Device device) {
//        return deviceMapper.selectDeviceList(device);
//    }
//
//
//    public List<Device> queryListBySn(String sn) {
//        Device device = new Device();
//        device.setSn(sn);
//        return deviceMapper.selectDeviceList(device);
//    }
//
//    /**
//     * 根据船舶SN和设备类型查询设备列表
//     * @param sn 船舶SN
//     * @param type 设备类型
//     * @return 设备列表
//     */
//    public List<Device> selectSimpleDeviceListBySnAndType(String sn, Long type) {
//        return deviceMapper.selectSimpleDeviceListBySnAndType(sn, type);
//    }
//
//    /**
//     * 查询设备列表
//     *
//     * @return 设备集合
//     */
//    public List<Device> selectAllDevice() {
//        return deviceMapper.selectDeviceList(null);
//    }
//
//    /**
//     * 根据 code查询设备信息
//     *
//     * @param code
//     * @return 设备信息
//     */
//    public Device selectByCodeAndSn(String sn, String code) {
//        Device device = new Device();
//        device.setSn(sn);
//        device.setCode(code);
//        return deviceMapper.selectDevice(device);
//    }
//
//    /**
//     * 根据设备编号查询设备
//     *
//     * @param code 设备编号
//     * @return 设备信息
//     */
//    public Device selectDeviceByCode(String code) {
//        return deviceMapper.selectDeviceByCode(code);
//    }
//
//    /**
//     * 更新redis中设备
//     */
//    public Device getDeviceBySnAndCode(String sn, String code) {
//        ValueOperations<String, Device> opsForValue = redisTemplate.opsForValue();
//        Device Device = opsForValue.get("ALL_DEVICE-" + sn + "_" + code);
//
//        if (Device != null) {
//            return Device;
//        }
//
//        List<Device> list = selectAllDevice();
//        for (Device device : list) {
//            opsForValue.set("ALL_DEVICE-" + device.getSn() + "_" + device.getCode(), device);
//        }
//
//        return opsForValue.get("ALL_DEVICE-" + sn + "_" + code);
//    }
//
//}
