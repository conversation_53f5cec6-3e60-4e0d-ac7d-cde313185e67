package com.snct.hbase.domain.hbase;

import com.snct.hbase.annotation.Excel;
import com.snct.hbase.annotation.HBaseColumn;
import com.snct.hbase.annotation.HBaseTable;


/**
 * Modem消息对象 bu_msg_modem
 *
 * <AUTHOR>
 * @date 2025-04-24
 */
@HBaseTable(tableName = "snct:modem")
public class ModemHbaseVo
{

    /** ID */
    @HBaseColumn(family = "rowkey", qualifier = "rowkey")
    private String id;
    /**
     * 录入时间(北京时间yyyy-MM-dd HH:mm:ss)
     */
    @Excel(name="录入时间")
    @HBaseColumn(family = "i", qualifier = "i_b_t")
    private String initialBjTime;

    /** 部门ID */
    @HBaseColumn(family = "i", qualifier = "d_p")
    private Long deptId;

    /** 部门名称 */
    @Excel(name="部门名称")
    @HBaseColumn(family = "i", qualifier = "d_p_n")
    private String deptName;

    /** 船只id */
    @HBaseColumn(family = "i", qualifier = "s_i")
    private Long shipId;

    /** 船只名称 */
    @Excel(name="船只名称")
    @HBaseColumn(family = "i", qualifier = "s_n")
    private String shipName;

    /** 设备id */
    @HBaseColumn(family = "i", qualifier = "d_i")
    private Long deviceId;

    /** 设备名称 */
    @Excel(name="设备名称")
    @HBaseColumn(family = "i", qualifier = "d_n")
    private String deviceName;

    /** 信号强度 */
    @Excel(name = "信号强度")
    @HBaseColumn(family = "i", qualifier = "s_g")
    private Double signal;

    /** 速率 */
    @Excel(name = "速率")
    @HBaseColumn(family = "i", qualifier = "s_d")
    private Double speed;

    /** 发送功率 */
    @Excel(name = "发送功率")
    @HBaseColumn(family = "i", qualifier = "s_p")
    private Double sendPower;

    /** 状态标志 */
    @Excel(name = "状态标志")
    @HBaseColumn(family = "i", qualifier = "i_f")
    private Long isFlag;

    /** 状态 0默认 1发送云端成功 2发送云端失败 */
    @HBaseColumn(family = "i", qualifier = "s_t")
    private Long status;

    public void setId(String id)
    {
        this.id = id;
    }

    public String getId()
    {
        return id;
    }

    public void setDeptId(Long deptId)
    {
        this.deptId = deptId;
    }

    public Long getDeptId()
    {
        return deptId;
    }

    public void setShipId(Long shipId)
    {
        this.shipId = shipId;
    }

    public Long getShipId()
    {
        return shipId;
    }

    public void setDeviceId(Long deviceId)
    {
        this.deviceId = deviceId;
    }

    public Long getDeviceId()
    {
        return deviceId;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getShipName() {
        return shipName;
    }

    public void setShipName(String shipName) {
        this.shipName = shipName;
    }

    public String getDeviceName() {
        return deviceName;
    }

    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    public void setSignal(Double signal)
    {
        this.signal = signal;
    }

    public Double getSignal()
    {
        return signal;
    }

    public void setSpeed(Double speed)
    {
        this.speed = speed;
    }

    public Double getSpeed()
    {
        return speed;
    }

    public void setSendPower(Double sendPower)
    {
        this.sendPower = sendPower;
    }

    public Double getSendPower()
    {
        return sendPower;
    }

    public void setIsFlag(Long isFlag)
    {
        this.isFlag = isFlag;
    }

    public Long getIsFlag()
    {
        return isFlag;
    }

    public void setStatus(Long status)
    {
        this.status = status;
    }

    public Long getStatus()
    {
        return status;
    }

    public String getInitialBjTime() {
        return initialBjTime;
    }

    public void setInitialBjTime(String initialBjTime) {
        this.initialBjTime = initialBjTime;
    }
}
