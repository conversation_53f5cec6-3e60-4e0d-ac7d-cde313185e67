package com.snct.web.controller.system;

import com.snct.common.config.SnctConfig;
import com.snct.common.core.controller.BaseController;
import com.snct.common.core.domain.AjaxResult;
import com.snct.common.utils.DateUtils;
import com.snct.system.service.ISysPostService;
import com.snct.utils.SysCmd;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.nio.file.Paths;

/**
 * 版本更新
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/upload")
public class SysUploadController extends BaseController {
    @Autowired
    private ISysPostService postService;

    @Autowired
    private SysCmd sysCmd;

    /**
     * 版本更新上传文件
     */
    @PostMapping("/v")
    public AjaxResult uploadFile(MultipartFile file) {
        try {
            // 上传文件路径
            String filePath = SnctConfig.getDevUrl();
            // 上传并返回新文件名称
            //System.out.println("filePath####:"+filePath+file.getOriginalFilename());
            File outFile = new File(filePath + "/snct-admin.jar");
            String cmd =
                    "mv " + filePath + "/snct-admin.jar " + filePath + "/snct-admin.jar." + DateUtils.parseTimeToDate(System.currentTimeMillis(), "yyyyMMddHHmmss");
            sysCmd.cmd(cmd);
            file.transferTo(Paths.get(outFile.getAbsolutePath()));
            //sysCmd.cmd("chmod +x "+filePath+"/snct-admin.jar ");
            return AjaxResult.success();
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 重启系统
     */
    @PostMapping("/reboot")
    public AjaxResult reboot() {
        sysCmd.reboot();
        return AjaxResult.success();
    }

    /**
     * 执行重启服务脚本
     */
    @PostMapping("/restart")
    public AjaxResult executeSystemReboot() {
        sysCmd.executeRestartScript("sh " + SnctConfig.getDevUrl() + "/restart.sh");
        return AjaxResult.success();
    }

    /**
     * 获取版本号
     */
    @PostMapping("/getVersion")
    public AjaxResult getVersion() {
        AjaxResult ajax = AjaxResult.success();
        ajax.put("version", SnctConfig.getVersion());
        return ajax;
    }


}
